import{r as t,M as h}from"./index.C1NIn1Y2.js";import{u as p}from"./FormClearHelper.D1M9GM_c.js";function v({getStateFromWidgetMgr:n,getDefaultState:c,updateWidgetMgrState:i,element:s,widgetMgr:u,fragmentId:l,onFormCleared:r}){const[V,o]=t.useState(()=>n(u,s)??c(u,s)),[f,a]=t.useState({value:V,fromUi:!1});t.useEffect(()=>{h(f)||(a(null),o(f.value),i(s,u,f,l))},[f,i,s,u,l]);const e=t.useCallback(()=>{a({value:c(u,s),fromUi:!0}),r==null||r()},[a,s,c,u,r]);return p({widgetMgr:u,element:s,onFormCleared:e}),[V,a]}function S({getStateFromWidgetMgr:n,getDefaultStateFromProto:c,getCurrStateFromProto:i,updateWidgetMgrState:s,element:u,widgetMgr:l,fragmentId:r,onFormCleared:V}){const o=t.useCallback((e,x)=>c(x),[c]),[f,a]=v({getStateFromWidgetMgr:n,getDefaultState:o,updateWidgetMgrState:s,element:u,widgetMgr:l,fragmentId:r,onFormCleared:V});return t.useEffect(()=>{u.setValue&&(u.setValue=!1,a({value:i(u),fromUi:!1}))},[u,i,a]),[f,a]}export{S as a,v as u};
