import{r,E as Q,_ as Y,cy as I,M as m,O as Z,l as mt,n as E,z as ht,aN as gt,co as yt,H as q,C as F,j as c,bp as It,bD as Tt,bq as wt,b7 as Ct,br as St,D as vt,bm as K}from"./index.C1NIn1Y2.js";import{u as Vt}from"./uniqueId.O0UbJ2Bu.js";import{u as xt}from"./FormClearHelper.D1M9GM_c.js";import{I as kt}from"./InputInstructions.DaZ89mzH.js";import{s as Dt}from"./sprintf.D7DtBTRn.js";import{I as Et}from"./input.CxKZ5Wrc.js";import"./base-input.BJ4qsfSq.js";var tt=r.forwardRef(function(t,e){var a={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return r.createElement(Q,Y({iconAttrs:a,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:e}),r.createElement("path",{d:"M0 3v2h8V3H0z"}))});tt.displayName="Minus";var et=r.forwardRef(function(t,e){var a={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return r.createElement(Q,Y({iconAttrs:a,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:e}),r.createElement("path",{d:"M3 0v3H0v2h3v3h2V5h3V3H5V0H3z"}))});et.displayName="Plus";const Rt=mt.getLogger("NumberInput");function Nt(t){return m(t)||t===""?void 0:t}const W=({value:t,format:e,step:a,dataType:b})=>{if(m(t))return null;let o=Nt(e);if(m(o)&&Z(a)){const d=a.toString();b===I.DataType.FLOAT&&a!==0&&d.includes(".")&&(o=`%0.${d.split(".")[1].length}f`)}if(m(o))return t.toString();try{return Dt.sprintf(o,t)}catch(d){return Rt.warn(`Error in sprintf(${o}, ${t}): ${d}`),String(t)}},zt=(t,e,a)=>m(t)?!1:t-e>=a,Ft=(t,e,a)=>m(t)?!1:t+e<=a,Wt=t=>(t.element.dataType===I.DataType.INT?t.widgetMgr.getIntValue(t.element):t.widgetMgr.getDoubleValue(t.element))??t.element.default??null,M=({step:t,dataType:e})=>t||(e===I.DataType.INT?1:.01),Lt=E("div",{target:"eaba2yi0"})(({theme:t})=>({display:"flex",flexDirection:"row",flexWrap:"nowrap",alignItems:"center",height:t.sizes.minElementHeight,borderWidth:t.sizes.borderWidth,borderStyle:"solid",borderColor:t.colors.widgetBorderColor??t.colors.secondaryBg,transitionDuration:"200ms",transitionProperty:"border",transitionTimingFunction:"cubic-bezier(0.2, 0.8, 0.4, 1)",borderRadius:t.radii.default,overflow:"hidden","&.focused":{borderColor:t.colors.primary},input:{MozAppearance:"textfield","&::-webkit-inner-spin-button, &::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:t.spacing.none}}})),Bt=E("div",{target:"eaba2yi1"})({display:"flex",flexDirection:"row",alignSelf:"stretch"}),J=E("button",{target:"eaba2yi2"})(({theme:t})=>({margin:t.spacing.none,border:"none",height:t.sizes.full,display:"flex",alignItems:"center",width:t.sizes.numberInputControlsWidth,justifyContent:"center",color:t.colors.bodyText,transition:"color 300ms, backgroundColor 300ms",backgroundColor:t.colors.secondaryBg,"&:hover:enabled, &:focus:enabled":{color:t.colors.white,backgroundColor:t.colors.primary,transition:"none",outline:"none"},"&:active":{outline:"none",border:"none"},"&:disabled":{cursor:"not-allowed",color:t.colors.fadedText40}})),Ht=E("div",{target:"eaba2yi3"})(({theme:t,clearable:e})=>({position:"absolute",marginRight:t.spacing.twoXS,left:0,right:`calc(${t.sizes.numberInputControlsWidth} * 2 + ${e?"1em":"0em"})`})),Pt=({disabled:t,element:e,widgetMgr:a,fragmentId:b})=>{var X;const o=ht(),{dataType:d,id:S,formId:p,default:L,format:B,icon:T,min:h,max:g}=e,[H,ot]=gt(),[s,rt]=r.useState(()=>M(e)),P=Wt({element:e,widgetMgr:a}),[y,w]=r.useState(!1),[l,C]=r.useState(P),[U,v]=r.useState(()=>W({value:P,...e,step:s})),[A,$]=r.useState(!1),V=r.useRef(null),O=r.useRef(Vt("number_input_")),x=zt(l,s,h),k=Ft(l,s,g),j=yt({formId:p}),nt=j?a.allowFormEnterToSubmit(p):y,at=A&&H>o.breakpoints.hideWidgetDetails;r.useEffect(()=>{rt(M({step:e.step,dataType:e.dataType}))},[e.dataType,e.step]);const u=r.useCallback(({value:n,source:i})=>{var f;if(Z(n)&&(h>n||n>g))(f=V.current)==null||f.reportValidity();else{const D=n??L??null;switch(d){case I.DataType.INT:a.setIntValue({id:S,formId:p},D,i,b);break;case I.DataType.FLOAT:a.setDoubleValue({id:S,formId:p},D,i,b);break;default:throw new Error("Invalid data type")}w(!1),C(D),v(W({value:D,dataType:d,format:B,step:s}))}},[h,g,V,a,b,s,d,S,p,L,B]),st=r.useCallback(()=>{y&&u({value:l,source:{fromUi:!0}}),$(!1)},[y,l,u]),it=r.useCallback(()=>{$(!0)},[]),_=r.useCallback(()=>{const{value:n}=e;e.setValue=!1,C(n??null),v(W({value:n??null,...e,step:s})),u({value:n??null,source:{fromUi:!1}})},[e,s,u]);r.useEffect(()=>{e.setValue?_():u({value:l,source:{fromUi:!1}});const n=V.current;if(n){const i=f=>{f.preventDefault()};return n.addEventListener("wheel",i),()=>{n.removeEventListener("wheel",i)}}},[]),e.setValue&&_();const R=m(e.default)&&!t,lt=r.useCallback(()=>{const n=e.default??null;C(n),u({value:n,source:{fromUi:!0}})},[e]);xt({element:e,widgetMgr:a,onFormCleared:lt});const ct=n=>{const{value:i}=n.target;if(i==="")w(!0),C(null),v(null);else{let f;e.dataType===I.DataType.INT?f=parseInt(i,10):f=parseFloat(i),w(!0),C(f),v(i)}},N=r.useCallback(()=>{k&&(w(!0),u({value:(l??h)+s,source:{fromUi:!0}}))},[l,h,s,k]),z=r.useCallback(()=>{x&&(w(!0),u({value:(l??g)-s,source:{fromUi:!0}}))},[l,g,s,x]),ut=r.useCallback(n=>{const{key:i}=n;switch(i){case"ArrowUp":n.preventDefault(),N();break;case"ArrowDown":n.preventDefault(),z();break}},[N,z]),dt=r.useCallback(n=>{n.key==="Enter"&&(y&&u({value:l,source:{fromUi:!0}}),a.allowFormEnterToSubmit(p)&&a.submitForm(p,b))},[y,l,u,a,p,b]),G=T==null?void 0:T.startsWith(":material"),pt=G?"lg":"base",ft=q(o.iconSizes.lg)+2*q(o.spacing.twoXS),bt=T?o.breakpoints.hideNumberInputControls+ft:o.breakpoints.hideNumberInputControls;return F("div",{className:"stNumberInput","data-testid":"stNumberInput",ref:ot,children:[c(St,{label:e.label,disabled:t,labelVisibility:It((X=e.labelVisibility)==null?void 0:X.value),htmlFor:O.current,children:e.help&&c(Tt,{children:c(wt,{content:e.help,placement:Ct.TOP_RIGHT})})}),F(Lt,{className:A?"focused":"","data-testid":"stNumberInputContainer",children:[c(Et,{type:"number",inputRef:V,value:U??"",placeholder:e.placeholder,onBlur:st,onFocus:it,onChange:ct,onKeyPress:dt,onKeyDown:ut,clearable:R,clearOnEscape:R,disabled:t,"aria-label":e.label,startEnhancer:e.icon&&c(vt,{"data-testid":"stNumberInputIcon",iconValue:e.icon,size:pt}),id:O.current,overrides:{ClearIconContainer:{style:{padding:0}},ClearIcon:{props:{overrides:{Svg:{style:{color:o.colors.darkGray,padding:o.spacing.threeXS,height:o.sizes.clearIconSize,width:o.sizes.clearIconSize,":hover":{fill:o.colors.bodyText}}}}}},Input:{props:{"data-testid":"stNumberInputField",step:s,min:h,max:g,type:"number",inputMode:""},style:{lineHeight:o.lineHeights.inputWidget,paddingRight:o.spacing.sm,paddingLeft:o.spacing.md,paddingBottom:o.spacing.sm,paddingTop:o.spacing.sm,"::placeholder":{color:o.colors.fadedText60}}},InputContainer:{style:()=>({borderTopRightRadius:0,borderBottomRightRadius:0})},Root:{style:{borderTopRightRadius:0,borderBottomRightRadius:0,borderTopLeftRadius:0,borderBottomLeftRadius:0,borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0,paddingRight:0,paddingLeft:T?o.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:o.iconSizes.lg,color:G?o.colors.fadedText60:"inherit"}}}}),H>bt&&F(Bt,{children:[c(J,{"data-testid":"stNumberInputStepDown",onClick:z,disabled:!x||t,tabIndex:-1,children:c(K,{content:tt,size:"xs",color:x?"inherit":o.colors.disabled})}),c(J,{"data-testid":"stNumberInputStepUp",onClick:N,disabled:!k||t,tabIndex:-1,children:c(K,{content:et,size:"xs",color:k?"inherit":o.colors.disabled})})]})]}),at&&c(Ht,{clearable:R,children:c(kt,{dirty:y,value:U??"",inForm:j,allowEnterToSubmit:nt})})]})},Xt=r.memo(Pt);export{Xt as default};
