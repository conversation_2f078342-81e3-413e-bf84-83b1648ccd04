import cv2
import base64
import json
from ultralytics import <PERSON><PERSON><PERSON>

def process_video(video_path):
    model = YOLO('best.pt')
    count = 0
    
    # Process video frames with YOLO tracking
    results = model.track(source=video_path, save=False, show=False, stream=True, tracker='botsort.yaml', conf=0.5)
    
    for result in results:
        frame = result.orig_img
        boxesData = result.boxes
        
        if boxesData.xywh is not None and boxesData.id is not None:
            boxes = boxesData.xywh.cpu().numpy()
            ids = boxesData.id.cpu().numpy()
            
            # Update maximum object ID count
            if len(ids) > 0:
                count = max(count, max(ids))
            
            # Draw bounding boxes and labels
            for i in range(len(boxes)):
                x_center, y_center, width, height = boxes[i]
                x1 = int(x_center - width / 2)
                y1 = int(y_center - height / 2)
                x2 = int(x_center + width / 2)
                y2 = int(y_center + height / 2)
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(frame, f'weed {int(ids[i])}', (x1, y1 + 20), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Encode frame as JPEG and convert to base64
        ret, buffer = cv2.imencode('.jpg', frame)
        frame_base64 = base64.b64encode(buffer).decode('utf-8')
        
        # Output data
        data = {'frame': frame_base64, 'count': int(count)}
        yield json.dumps(data)

# Example usage
if __name__ == '__main__':
    video_path = 'uploads/uploaded_video.mp4'
    for data in process_video(video_path):
        print(data)  # Or handle the JSON data as needed