import{r,b as z}from"./index.C1NIn1Y2.js";import{s as J,i as w,a as Q,b as U,T as Z,C as ee,m as te}from"./index.BUq9Wcf8.js";import"./FormClearHelper.D1M9GM_c.js";import"./withFullScreenWrapper.iW37lS8Z.js";import"./Toolbar.KhlcEc0K.js";import"./checkbox.DSDh78Xz.js";import"./mergeWith.CVkhrWUb.js";import"./sprintf.D7DtBTRn.js";import"./createDownloadLinkElement.DZMwyjvU.js";import"./toConsumableArray.BZoworE-.js";import"./possibleConstructorReturn.CIDCId52.js";import"./createSuper.wQ9SIXEJ.js";import"./FileDownload.esm.AI3watX9.js";const re=()=>t=>t.targetX,ie=()=>t=>t.targetY,ne=()=>t=>t.targetWidth,se=()=>t=>t.targetHeight,ae=()=>t=>t.targetY+10,oe=()=>t=>Math.max(0,(t.targetHeight-28)/2),de=J("div")({name:"DataGridOverlayEditorStyle",class:"gdg-d19meir1",propsAsIs:!1,vars:{"d19meir1-0":[ie(),"px"],"d19meir1-1":[re(),"px"],"d19meir1-2":[ne(),"px"],"d19meir1-3":[se(),"px"],"d19meir1-4":[ae(),"px"],"d19meir1-5":[oe(),"px"]}});function le(){const[t,s]=r.useState();return[t??void 0,s]}function ue(){const[t,s]=le(),[n,y]=r.useState(0),[g,x]=r.useState(!0);r.useLayoutEffect(()=>{if(t===void 0||!("IntersectionObserver"in window))return;const a=new IntersectionObserver(o=>{o.length!==0&&x(o[0].isIntersecting)},{threshold:1});return a.observe(t),()=>a.disconnect()},[t]),r.useEffect(()=>{if(g||t===void 0)return;let a;const o=()=>{const{right:v}=t.getBoundingClientRect();y(p=>Math.min(p+window.innerWidth-v-10,0)),a=requestAnimationFrame(o)};return a=requestAnimationFrame(o),()=>{a!==void 0&&cancelAnimationFrame(a)}},[t,g]);const O=r.useMemo(()=>({transform:`translateX(${n}px)`}),[n]);return{ref:s,style:O}}const Se=t=>{const{target:s,content:n,onFinishEditing:y,forceEditMode:g,initialValue:x,imageEditorOverride:O,markdownDivCreateNode:a,highlight:o,className:v,theme:p,id:H,cell:S,bloom:c,validateCell:d,getCellRenderer:F,provideEditor:h,isOutsideClick:X,customEventTarget:A}=t,[l,K]=r.useState(g?n:void 0),k=r.useRef(l??n);k.current=l??n;const[E,R]=r.useState(()=>d===void 0?!0:!(w(n)&&(d==null?void 0:d(S,n,k.current))===!1)),f=r.useCallback((e,i)=>{y(E?e:void 0,i)},[E,y]),W=r.useCallback(e=>{if(d!==void 0&&e!==void 0&&w(e)){const i=d(S,e,k.current);i===!1?R(!1):(typeof i=="object"&&(e=i),R(!0))}K(e)},[S,d]),C=r.useRef(!1),m=r.useRef(void 0),Y=r.useCallback(()=>{f(l,[0,0]),C.current=!0},[l,f]),j=r.useCallback((e,i)=>{f(e,i??m.current??[0,0]),C.current=!0},[f]),q=r.useCallback(async e=>{let i=!1;e.key==="Escape"?(e.stopPropagation(),e.preventDefault(),m.current=[0,0]):e.key==="Enter"&&!e.shiftKey?(e.stopPropagation(),e.preventDefault(),m.current=[0,1],i=!0):e.key==="Tab"&&(e.stopPropagation(),e.preventDefault(),m.current=[e.shiftKey?-1:1,0],i=!0),window.setTimeout(()=>{!C.current&&m.current!==void 0&&(f(i?l:void 0,m.current),C.current=!0)},0)},[f,l]),D=l??n,[u,B]=r.useMemo(()=>{var i,G;if(Q(n))return[];const e=h==null?void 0:h(n);return e!==void 0?[e,!1]:[(G=(i=F(n))==null?void 0:i.provideEditor)==null?void 0:G.call(i,n),!1]},[n,F,h]),{ref:L,style:$}=ue();let P=!0,T,M=!0,b;if(u!==void 0){P=u.disablePadding!==!0,M=u.disableStyling!==!0;const e=U(u);e&&(b=u.styleOverride);const i=e?u.editor:u;T=r.createElement(i,{isHighlighted:o,onChange:W,value:D,initialValue:x,onFinishedEditing:j,validatedSelection:w(D)?D.selectionRange:void 0,forceEditMode:g,target:s,imageEditorOverride:O,markdownDivCreateNode:a,isValid:E,theme:p})}b={...b,...$};const _=document.getElementById("portal");if(_===null)return console.error('Cannot open Data Grid overlay editor, because portal not found.  Please add `<div id="portal" />` as the last child of your `<body>`.'),null;let I=M?"gdg-style":"gdg-unstyle";E||(I+=" gdg-invalid"),P&&(I+=" gdg-pad");const N=(c==null?void 0:c[0])??1,V=(c==null?void 0:c[1])??1;return z.createPortal(r.createElement(Z.Provider,{value:p},r.createElement(ee,{style:te(p),className:v,onClickOutside:Y,isOutsideClick:X,customEventTarget:A},r.createElement(de,{ref:L,id:H,className:I,style:b,as:B===!0?"label":void 0,targetX:s.x-N,targetY:s.y-V,targetWidth:s.width+N*2,targetHeight:s.height+V*2},r.createElement("div",{className:"gdg-clip-region",onKeyDown:q},T)))),_)};export{Se as default};
